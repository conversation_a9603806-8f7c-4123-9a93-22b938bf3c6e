/**
 * ApiClient - 统一的API客户端
 * 负责处理所有HTTP请求，包括认证、错误处理、超时等
 */
class ApiClient {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = null;
    this.defaultTimeout = 10000;
    this.requestInterceptors = [];
    this.responseInterceptors = [];
  }

  /**
   * 设置认证Token
   * @param {string} token - JWT Token
   */
  setToken(token) {
    this.token = token;
    console.log('ApiClient: Token updated');
  }

  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * 添加请求拦截器
   * @param {Function} interceptor - 拦截器函数
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * 添加响应拦截器
   * @param {Function} interceptor - 拦截器函数
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * 执行请求拦截器
   * @param {Object} config - 请求配置
   * @returns {Object} 处理后的配置
   */
  async executeRequestInterceptors(config) {
    let processedConfig = config;
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig);
    }
    return processedConfig;
  }

  /**
   * 执行响应拦截器
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的响应
   */
  async executeResponseInterceptors(response) {
    let processedResponse = response;
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse);
    }
    return processedResponse;
  }

  /**
   * 核心请求方法
   * @param {string} endpoint - API端点
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求Promise
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    let config = {
      timeout: this.defaultTimeout,
      headers: this.getHeaders(),
      ...options,
    };

    // 执行请求拦截器
    config = await this.executeRequestInterceptors(config);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, config.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // 检查HTTP状态
      if (!response.ok) {
        throw new ApiError(
          `HTTP_${response.status}`,
          `HTTP ${response.status}: ${response.statusText}`,
          response.status
        );
      }

      // 解析JSON响应
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        throw new ApiError(
          'PARSE_ERROR',
          'Failed to parse response as JSON',
          response.status
        );
      }

      // 执行响应拦截器
      data = await this.executeResponseInterceptors(data);

      // 检查业务状态
      if (data.ok !== 1) {
        throw new ApiError(
          'API_ERROR',
          data.err || data.msg || 'API请求失败',
          response.status,
          data
        );
      }

      return data.data;

    } catch (error) {
      if (error.name === 'AbortError') {
        throw new ApiError('TIMEOUT', '请求超时', 408);
      }
      
      if (error instanceof ApiError) {
        throw error;
      }

      // 网络错误或其他未知错误
      throw new ApiError(
        'NETWORK_ERROR',
        error.message || '网络请求失败',
        0
      );
    }
  }

  /**
   * GET请求
   * @param {string} endpoint - API端点
   * @param {Object} params - 查询参数
   * @param {Object} options - 请求选项
   * @returns {Promise}
   */
  async get(endpoint, params = {}, options = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    
    return this.request(url, {
      method: 'GET',
      ...options,
    });
  }

  /**
   * POST请求
   * @param {string} endpoint - API端点
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise}
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  }

  /**
   * PUT请求
   * @param {string} endpoint - API端点
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise}
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  }

  /**
   * DELETE请求
   * @param {string} endpoint - API端点
   * @param {Object} options - 请求选项
   * @returns {Promise}
   */
  async delete(endpoint, options = {}) {
    return this.request(endpoint, {
      method: 'DELETE',
      ...options,
    });
  }

  /**
   * 检查网络连接状态
   * @returns {Promise<boolean>}
   */
  async checkConnection() {
    try {
      await this.get('/health', {}, { timeout: 5000 });
      return true;
    } catch (error) {
      console.warn('Network check failed:', error.message);
      return false;
    }
  }
}

/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(code, message, status = 0, data = null) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.status = status;
    this.data = data;
  }

  /**
   * 判断是否为网络错误
   * @returns {boolean}
   */
  isNetworkError() {
    return this.code === 'NETWORK_ERROR' || this.code === 'TIMEOUT';
  }

  /**
   * 判断是否为认证错误
   * @returns {boolean}
   */
  isAuthError() {
    return this.status === 401 || this.status === 403;
  }

  /**
   * 判断是否为服务器错误
   * @returns {boolean}
   */
  isServerError() {
    return this.status >= 500;
  }

  /**
   * 获取用户友好的错误消息
   * @returns {string}
   */
  getUserMessage() {
    switch (this.code) {
      case 'TIMEOUT':
        return '请求超时，请检查网络连接';
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络设置';
      case 'HTTP_401':
        return '认证失败，请重新登录';
      case 'HTTP_403':
        return '权限不足，无法访问该资源';
      case 'HTTP_404':
        return '请求的资源不存在';
      case 'HTTP_500':
        return '服务器内部错误，请稍后重试';
      default:
        return this.message || '请求失败，请稍后重试';
    }
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient('http://192.168.0.193:8080');

// 添加默认的请求拦截器（记录请求日志）
apiClient.addRequestInterceptor(async (config) => {
  console.log(`API Request: ${config.method || 'GET'} ${config.url}`);
  return config;
});

// 添加默认的响应拦截器（记录响应日志）
apiClient.addResponseInterceptor(async (response) => {
  console.log('API Response received');
  return response;
});

export { ApiError };
