/**
 * @file Manages all interactions with the backend API.
 * Handles video interactions, user states, and property data.
 */

// Property fetching is handled directly by this service
// No external dependencies needed

/**
 * API Service for backend interactions
 */
class ApiService {
  constructor() {
    this.baseURL = 'http://192.168.2.162:8080';
    this.token = null;
  }

  /**
   * Set JWT token for authentication
   * @param {string} token - JWT token
   */
  setToken(token) {
    this.token = token;
    console.log('ApiService: Token updated');
  }

  /**
   * Get authentication headers
   * @returns {Object}
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make API request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise}
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: this.getHeaders(),
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.ok !== 1) {
        throw new Error(data.err || data.msg || 'API请求失败');
      }

      return data.data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * Create interaction event
   * @param {string} videoId - Video ID
   * @param {string} type - Interaction type
   * @param {Object} meta - Metadata
   * @returns {Promise<boolean>}
   */
  async createInteraction(videoId, type, meta = {}) {
    try {
      await this.request('/video/public/interactions', {
        method: 'POST',
        body: JSON.stringify({
          videoId,
          type,
          meta,
        }),
      });
      return true;
    } catch (error) {
      console.error('Create interaction failed:', error);
      return false;
    }
  }

  /**
   * Get user states for videos
   * @param {Array<string>} videoIds - Video IDs
   * @returns {Promise<Array>}
   */
  async getUserStates(videoIds) {
    if (!videoIds || videoIds.length === 0) {
      return [];
    }

    try {
      const userId = this.extractUserIdFromToken();
      if (!userId) {
        console.warn('No user ID found in token');
        return [];
      }

      const data = await this.request('/video/public/states/batch', {
        method: 'POST',
        body: JSON.stringify({
          userId,
          videoIds,
        }),
      });

      return data.states || [];
    } catch (error) {
      console.error('Get user states failed:', error);
      return [];
    }
  }

  /**
   * Extract user ID from JWT token
   * @returns {string|null}
   */
  extractUserIdFromToken() {
    if (!this.token) return null;

    try {
      const payload = JSON.parse(atob(this.token.split('.')[1]));
      return payload.sub;
    } catch (error) {
      console.error('Failed to parse JWT token:', error);
      return null;
    }
  }

  /**
   * Get properties by IDs
   * @param {Array<string>} propertyIds - Property IDs
   * @returns {Promise<Array>}
   */
  async getPropertiesByIds(propertyIds) {
    if (!propertyIds || propertyIds.length === 0) {
      return [];
    }

    try {
      console.log('ApiService: Fetching properties for IDs:', propertyIds);

      const data = await this.request('/video/admin/properties/batch-get', {
        method: 'POST',
        body: JSON.stringify({
          ids: propertyIds
        }),
      });

      // Backend returns properties array directly in data field
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Get properties failed:', error);
      return [];
    }
  }

  /**
   * Get single property by ID
   * @param {string} propertyId - Property ID
   * @returns {Promise<Object|null>}
   */
  async getPropertyById(propertyId) {
    if (!propertyId) {
      return null;
    }

    try {
      console.log('ApiService: Fetching single property:', propertyId);

      const data = await this.request(`/video/admin/properties/${propertyId}`);
      return data;
    } catch (error) {
      console.error('Get single property failed:', error);
      return null;
    }
  }

  /**
   * Search properties
   * @param {string} keyword - Search keyword
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>}
   */
  async searchProperties(keyword, page = 1, limit = 20) {
    if (!keyword) {
      return { properties: [], pagination: {} };
    }

    try {
      console.log('ApiService: Searching properties with keyword:', keyword);

      const data = await this.request('/video/admin/properties/search', {
        method: 'POST',
        body: JSON.stringify({
          s: keyword  // Backend expects 's' field for search
        }),
      });

      // Backend returns properties array directly in data field
      return {
        properties: Array.isArray(data) ? data : [],
        pagination: {}
      };
    } catch (error) {
      console.error('Search properties failed:', error);
      return { properties: [], pagination: {} };
    }
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Make it globally available
window.apiService = apiService;

/**
 * Fetches property data directly from backend API.
 * @param {string[]} propertyIds An array of property IDs.
 * @returns {Promise<{ok: number, data: object[]}>}
 */
export async function fetchPropertiesByIds(propertyIds) {
  try {
    console.log('M1 Player: Fetching properties directly from API');

    // Use apiService to fetch properties directly
    const properties = await apiService.getPropertiesByIds(propertyIds);

    console.log(`M1 Player: Fetched ${properties.length} properties from API`);

    return {
      ok: 1,
      data: properties
    };
  } catch (error) {
    console.error('M1 Player: Failed to fetch properties from API:', error);

    // Return empty data on error
    return { ok: 1, data: [] };
  }
}