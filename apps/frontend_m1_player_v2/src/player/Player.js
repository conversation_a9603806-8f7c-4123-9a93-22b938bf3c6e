import { DataManager } from '../data/DataManager.js';
import { createVideoElements, updateVideoSlot } from '../ui/PlayerUI.js';
import { GestureManager } from '../gestures/GestureManager.js';
import { BottomMenu } from '../ui/BottomMenu.js';
import { HouseDrawer } from '../ui/HouseDrawer.js';
import { NUM_VIDEO_SLOTS, PRELOAD_THRESHOLD, DOUBLE_TAP_SEEK_SECONDS, MOBILE_PERFORMANCE_CONFIG } from '../config/constants.js';

// Debounce function for video switching performance
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
import { FullscreenController } from './FullscreenController.js';
import { AutoplayHelper } from '../utils/autoplayHelper.js';
import { PerformanceHelper } from '../utils/performanceHelper.js';

export class Player {
  constructor(container, initialVideos, startIndex, requestNextPageFn) {
    if (!container) throw new Error('Player requires a container element.');
    
    this.container = container;
    this.dataManager = new DataManager(initialVideos, requestNextPageFn);
    this.currentIndex = startIndex;
    this.isPlaying = true;
    this.progressMap = new Map();
    this.houseDrawers = [];
    this.isFullscreen = false;
    this.fullscreenController = null;
    
    // Bind event handlers once
    this.boundHandleClick = this.handleClick.bind(this);
    this.boundHandleChangeVideo = this.handleChangeVideo.bind(this);

    // Create debounced version for performance
    this.debouncedHandleChangeVideo = debounce(this.boundHandleChangeVideo, MOBILE_PERFORMANCE_CONFIG.VIDEO_SWITCH_DEBOUNCE);

    this.init();
  }

  async init() {
    console.log('[Player] init started.');

    // Initialize performance monitoring
    PerformanceHelper.init();

    await this.ensureDataReady();
    console.log('[Player] Data is ready.');

    const { wrapper, videoItems } = createVideoElements(NUM_VIDEO_SLOTS, this.container);
    this.videoItems = videoItems;
    this.wrapper = wrapper;
    console.log('[Player] UI elements created.');

    // Create a HouseDrawer instance for each video slot
    this.videoItems.forEach(item => {
      const drawerContainer = item.querySelector('.house-drawer-container');
      this.houseDrawers.push(new HouseDrawer(drawerContainer));
    });

    this.virtualize();
    console.log('[Player] Virtualization complete.');

    this.bottomMenu = new BottomMenu(this.container, this);
    console.log('[Player] BottomMenu initialized.');

    this.gestureManager = new GestureManager(
      this.wrapper, 
      this.currentIndex, 
      this.dataManager.getTotalVideos.bind(this.dataManager),
      this.togglePlayPause.bind(this),
      this.handleDoubleTap.bind(this),
      this.handleLongPress.bind(this)
    );
    console.log('[Player] GestureManager initialized.');
    
    this.container.addEventListener('changevideo', this.debouncedHandleChangeVideo);
    this.wrapper.addEventListener('click', this.boundHandleClick);

    // Load user states for videos
    this.loadUserStates();

    this.playCurrentVideo();
    console.log('[Player] init finished.');
  }

  handleClick(event) {
    this.handleSidebarInteraction(event);
    this.handleInfoToggle(event);
  }

  async ensureDataReady() {
    while (this.currentIndex >= this.dataManager.getTotalVideos() && this.dataManager.hasMoreData) {
      await this.dataManager.requestMoreVideos();
    }
  }

  handleChangeVideo(newIndexOrEvent) {
    const newIndex = typeof newIndexOrEvent === 'number' ? newIndexOrEvent : newIndexOrEvent.detail.index;

    this.saveCurrentProgress();

    const oldIndex = this.currentIndex;
    this.currentIndex = newIndex;

    // Record performance stats
    PerformanceHelper.recordVideoSwitch();

    this.virtualize(oldIndex);
    this.playCurrentVideo();
    this.preloadNextData();

    // Optimize video loading based on current position
    PerformanceHelper.optimizeVideoLoading(this.videoItems, Math.floor(NUM_VIDEO_SLOTS / 2));
  }

  virtualize(oldIndex) {
    const totalVideos = this.dataManager.getTotalVideos();
    this.videoItems.forEach((item, i) => {
      const dataIndex = this.currentIndex - Math.floor(NUM_VIDEO_SLOTS / 2) + i;
      const videoData = this.dataManager.getVideo(dataIndex);
      if (videoData) {
        updateVideoSlot(item, videoData, window.currentLanguage || 'en');
        item.style.transform = `translateY(${dataIndex * 100}%)`;
        item.style.display = 'block';
      } else {
        item.style.display = 'none';
      }
    });
  }

  playCurrentVideo() {
    this.videoItems.forEach(item => {
      const video = item.querySelector('video');
      this.removeProgressBarListeners(video);
      if (!video.paused) {
        video.pause();
      }
      const icon = item.querySelector('.play-icon');
      if (icon) icon.style.display = 'none';
      const overlay = item.querySelector('.video-overlay');
      if (overlay) overlay.classList.remove('visible');
    });

    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentSlot = this.videoItems[centerSlotIndex];
    if (!currentSlot) return;

    const currentData = this.dataManager.getVideo(this.currentIndex);
    if (currentData) {
      updateVideoSlot(currentSlot, currentData, window.currentLanguage || 'en');
    }

    const videoToPlay = currentSlot.querySelector('video');
    const icon = currentSlot.querySelector('.play-icon');
    const overlay = currentSlot.querySelector('.video-overlay');

    this.setupProgressBar(currentSlot);

    // Load house drawer for the current video
    const currentDrawer = this.houseDrawers[centerSlotIndex];
    if (currentDrawer) {
      currentDrawer.load(currentData?.propertyIds);
    }

    if (currentData) {
      const startPlayback = async () => {
        const currentCenterSlot = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)];
        if (currentCenterSlot.querySelector('video') === videoToPlay) {
          const savedTime = this.progressMap.get(currentData.id) || 0;
          videoToPlay.currentTime = savedTime;

          // Use AutoplayHelper for better WebView compatibility
          const autoplaySuccess = await AutoplayHelper.webViewAutoplay(videoToPlay);

          if (autoplaySuccess) {
            this.isPlaying = true;
            if (icon) icon.style.display = 'none';
            if (overlay) overlay.classList.remove('visible');

            // Start tracking progress when video starts playing
            this.trackVideoProgress();
          } else {
            console.warn(`Autoplay for ${videoToPlay.src} was prevented, showing controls`);
            if (icon) icon.style.display = 'block';
            if (overlay) overlay.classList.add('visible');
            this.isPlaying = false;

            // Stop tracking progress when video is not playing
            this.stopTrackingProgress();
          }
        }
        videoToPlay.removeEventListener('canplay', startPlayback);
      };

      videoToPlay.addEventListener('canplay', startPlayback);
      if (videoToPlay.readyState > 2) {
        startPlayback();
      }
    }
  }

  saveCurrentProgress() {
    const videoSlot = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)];
    if (!videoSlot) return;
    const video = videoSlot.querySelector('video');
    const videoData = this.dataManager.getVideo(this.currentIndex);
    if (video && videoData && video.currentTime > 0) {
      this.progressMap.set(videoData.id, video.currentTime);
    }
  }

  preloadNextData() {
    if (this.currentIndex >= this.dataManager.getTotalVideos() - PRELOAD_THRESHOLD) {
      this.dataManager.requestMoreVideos();
    }
  }

  async handleSidebarInteraction(event) {
    const button = event.target.closest('.sidebar-button');
    if (!button) return;
    event.stopPropagation();

    const action = button.dataset.action;
    const currentVideoData = this.dataManager.getVideo(this.currentIndex);
    if (!currentVideoData) return;

    // Add loading state to button
    const originalText = button.textContent;
    button.disabled = true;

    try {
      switch (action) {
        case 'like': {
          console.log('Player: Toggling like for video', currentVideoData.id);
          const newLikedState = await this.dataManager.toggleVideoLike(currentVideoData.id);
          console.log('Player: Like toggled to', newLikedState);
          break;
        }
        case 'collect': {
          console.log('Player: Toggling collection for video', currentVideoData.id);
          const newCollectedState = await this.dataManager.toggleVideoCollection(currentVideoData.id);
          console.log('Player: Collection toggled to', newCollectedState);
          break;
        }
        case 'share': {
          console.log('Player: Share action triggered for video', currentVideoData.id);
          // Create share interaction
          await this.dataManager.updateVideoUserState(currentVideoData.id, 'share', {
            sharedAt: new Date().toISOString()
          });
          break;
        }
        case 'more': {
          this.bottomMenu.show();
          break;
        }
        case 'fullscreen': {
          this.toggleFullscreen();
          break;
        }
        default: return;
      }

    } catch (error) {
      console.error('Player: Sidebar interaction failed:', error);
    } finally {
      // Restore button state
      button.disabled = false;
    }

    // Update UI to reflect changes
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentSlot = this.videoItems[centerSlotIndex];
    updateVideoSlot(currentSlot, currentVideoData, window.currentLanguage || 'en');
  }

  handleInfoToggle(event) {
    const toggleButton = event.target.closest('.more-toggle');
    if (!toggleButton) return;
    event.stopPropagation();
    const descriptionWrapper = toggleButton.parentElement;
    const isExpanded = descriptionWrapper.classList.toggle('expanded');
    toggleButton.textContent = isExpanded ? 'Less' : 'More';

    console.log('[Player] handleInfoToggle called, isExpanded:', isExpanded);

    // Update drawer position after info toggle
    // Find the current video element (center slot)
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentVideoElement = this.videoItems[centerSlotIndex];

    console.log('[Player] Current video element found:', !!currentVideoElement);

    if (currentVideoElement) {
      setTimeout(() => {
        this.updateDrawerPosition(currentVideoElement);
      }, 50); // Small delay to ensure DOM has updated
    }
  }

  handleLongPress() {
    this.bottomMenu.show();
  }

  updateDrawerPosition(videoElement) {
    if (!videoElement) {
      console.log('[Player] updateDrawerPosition: No videoElement provided');
      return;
    }

    const infoContainer = videoElement.querySelector('.video-info');
    const drawerContainer = videoElement.querySelector('.house-drawer-container');
    const drawerToggle = videoElement.querySelector('.house-drawer-toggle');
    const descriptionWrapper = videoElement.querySelector('.video-description-wrapper');

    console.log('[Player] updateDrawerPosition elements found:', {
      infoContainer: !!infoContainer,
      drawerContainer: !!drawerContainer,
      drawerToggle: !!drawerToggle,
      descriptionWrapper: !!descriptionWrapper
    });

    if (!infoContainer || !drawerContainer || !drawerToggle || !descriptionWrapper) {
      console.log('[Player] updateDrawerPosition: Missing required elements');
      return;
    }

    try {
      const isInfoExpanded = descriptionWrapper.classList.contains('expanded');
      const defaultBottomVh = 20; // Default position in vh
      const minSpacingPx = 5; // Minimum spacing in pixels

      console.log('[Player] updateDrawerPosition:', {
        isInfoExpanded,
        defaultBottomVh,
        minSpacingPx
      });

      if (!isInfoExpanded) {
        // Info is collapsed, restore drawer to default position
        drawerContainer.style.bottom = `${defaultBottomVh}vh`;
        drawerToggle.style.bottom = `${defaultBottomVh}vh`;
        console.log('[Player] Restored drawer to default position');
        return;
      }

      // Info is expanded, check if drawer needs to be pushed up
      const infoRect = infoContainer.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Calculate drawer's current position in pixels from bottom
      const drawerCurrentBottomPx = (defaultBottomVh / 100) * viewportHeight;
      const drawerCurrentTopPx = viewportHeight - drawerCurrentBottomPx;

      // Calculate info's top position
      const infoTopPx = infoRect.top;

      console.log('[Player] Position calculations:', {
        viewportHeight,
        infoTopPx,
        drawerCurrentBottomPx,
        drawerCurrentTopPx,
        wouldOverlap: drawerCurrentTopPx <= (infoTopPx + minSpacingPx)
      });

      // Check if drawer would overlap with info (with minimum spacing)
      // Drawer overlaps if its top position is greater than info's top position (drawer is below info)
      if (drawerCurrentTopPx >= (infoTopPx - minSpacingPx)) {
        // Drawer needs to be pushed up
        const distanceFromBottom = viewportHeight - infoTopPx;
        const newBottomVh = ((distanceFromBottom + minSpacingPx) / viewportHeight) * 100;

        drawerContainer.style.bottom = `${newBottomVh}vh`;
        drawerToggle.style.bottom = `${newBottomVh}vh`;

        console.log(`[Player] Pushed drawer up to ${newBottomVh.toFixed(1)}vh to avoid overlap`);
      } else {
        // No overlap, keep drawer at default position
        drawerContainer.style.bottom = `${defaultBottomVh}vh`;
        drawerToggle.style.bottom = `${defaultBottomVh}vh`;
        console.log('[Player] No overlap detected, drawer stays at default position');
      }
    } catch (error) {
      console.error('[Player] Error updating drawer position:', error);
    }
  }

  handleDoubleTap(event) {
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentSlot = this.videoItems[centerSlotIndex];
    const video = currentSlot.querySelector('video');
    const containerWidth = this.container.offsetWidth;
    const tapPositionX = event.center.x;

    if (tapPositionX < containerWidth / 2) {
      video.currentTime = Math.max(0, video.currentTime - DOUBLE_TAP_SEEK_SECONDS);
      this.showSeekIndicator(`-${DOUBLE_TAP_SEEK_SECONDS}s`);
    } else {
      video.currentTime = Math.min(video.duration, video.currentTime + DOUBLE_TAP_SEEK_SECONDS);
      this.showSeekIndicator(`+${DOUBLE_TAP_SEEK_SECONDS}s`);
    }
  }

  showSeekIndicator(text) {
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentSlot = this.videoItems[centerSlotIndex];
    if (!currentSlot) return;

    const indicator = currentSlot.querySelector('.seek-indicator');
    if (indicator) {
      indicator.textContent = text;
      indicator.classList.add('active');
      setTimeout(() => {
        indicator.classList.remove('active');
      }, 500);
    }
  }

  toggleFullscreen() {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.enterFullscreen();
    }
  }

  enterFullscreen() {
    console.log('[Player] Entering fullscreen mode');
    this.isFullscreen = true;
    
    // 添加CSS类
    this.container.classList.add('fullscreen-mode');
    document.body.classList.add('h5-in-fullscreen');
    
    // 通知React Native
    this.notifyReactNative('FULLSCREEN_CHANGE', {
      isFullscreen: true,
      orientation: 'landscape'
    });

    // Initialize the fullscreen controller
    this.fullscreenController = new FullscreenController(this.container, this);
    this.gestureManager.setFullscreenMode(true, this.fullscreenController.toggleControls.bind(this.fullscreenController));
  }

  exitFullscreen() {
    console.log('[Player] Exiting fullscreen mode');
    this.isFullscreen = false;
    
    // 移除CSS类
    this.container.classList.remove('fullscreen-mode');
    document.body.classList.remove('h5-in-fullscreen');
    
    // 通知React Native
    this.notifyReactNative('FULLSCREEN_CHANGE', {
      isFullscreen: false,
      orientation: 'portrait'
    });

    // Destroy the fullscreen controller
    if (this.fullscreenController) {
      this.fullscreenController.destroy();
      this.fullscreenController = null;
    }
    this.gestureManager.setFullscreenMode(false, null);
  }

  // React Native通信方法
  notifyReactNative(type, data) {
    if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
      const message = {
        type,
        ...data,
        videoId: this.dataManager.getVideo(this.currentIndex)?.id || '',
        timestamp: Date.now()
      };
      window.ReactNativeWebView.postMessage(JSON.stringify(message));
      console.log('[Player] Sent message to React Native:', message);
    } else {
      console.log('[Player] React Native WebView not available, message:', { type, ...data });
    }
  }

  togglePlayPause() {
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentSlot = this.videoItems[centerSlotIndex];
    const video = currentSlot.querySelector('video');
    const icon = currentSlot.querySelector('.play-icon');
    const overlay = currentSlot.querySelector('.video-overlay');

    if (video.paused) {
      video.play();
      icon.style.display = 'none';
      overlay.classList.remove('visible');
      this.isPlaying = true;
    } else {
      this.saveCurrentProgress();
      video.pause();
      icon.style.display = 'block';
      overlay.classList.add('visible');
      this.isPlaying = false;
    }
  }

  toggleMute() {
    const video = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');
    video.muted = !video.muted;
    return video.muted;
  }

  isMuted() {
    const video = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');
    return video.muted;
  }

  setPlaybackRate(rate) {
    const video = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)].querySelector('video');
    video.playbackRate = rate;
  }

  getPlaybackRate() {
    const video = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)]?.querySelector('video');
    return video ? video.playbackRate : 1.0;
  }

  setupProgressBar(videoSlot) {
    const video = videoSlot.querySelector('video');
    const seekBar = videoSlot.querySelector('.seek-bar');
    if (!video || !seekBar) return;
  
    const updateProgress = () => {
      if (isNaN(video.duration)) return;
      const percentage = (video.currentTime / video.duration) * 100;
      videoSlot.querySelector('.seek-bar-fill').style.width = `${percentage}%`;
      videoSlot.querySelector('.seek-bar-thumb').style.left = `${percentage}%`;
      videoSlot.querySelector('.time-display').textContent = 
        `${this.formatTime(video.currentTime)} / ${this.formatTime(video.duration)}`;
    };

    const onMetadataLoaded = () => updateProgress();
    video.addEventListener('timeupdate', updateProgress);
    video.addEventListener('loadedmetadata', onMetadataLoaded);

    video.progressListeners = { timeupdate: updateProgress, loadedmetadata: onMetadataLoaded };

    let isSeeking = false;
    const handleSeek = (event) => {
      if (isNaN(video.duration)) return;
      const seekBarRect = seekBar.getBoundingClientRect();
      const clickPosition = (event.clientX - seekBarRect.left) / seekBarRect.width;
      video.currentTime = Math.max(0, Math.min(clickPosition * video.duration, video.duration));
      updateProgress();
    };

    const onPointerMove = (moveEvent) => {
      if (isSeeking) handleSeek(moveEvent);
    };

    const onPointerUp = () => {
      if(isSeeking) {
        isSeeking = false;
        video.play().catch(e => console.warn("Play after seek failed", e));
        document.removeEventListener('pointermove', onPointerMove);
        document.removeEventListener('pointerup', onPointerUp);
      }
    };

    seekBar.addEventListener('pointerdown', (e) => {
      e.stopPropagation();
      isSeeking = true;
      video.pause();
      handleSeek(e);
      document.addEventListener('pointermove', onPointerMove);
      document.addEventListener('pointerup', onPointerUp);
    });
  }

  removeProgressBarListeners(video) {
    if (video && video.progressListeners) {
      video.removeEventListener('timeupdate', video.progressListeners.timeupdate);
      video.removeEventListener('loadedmetadata', video.progressListeners.loadedmetadata);
      delete video.progressListeners;
    }
  }

  formatTime(seconds) {
    return new Date(seconds * 1000).toISOString().substr(14, 5);
  }

  updateLanguage(language) {
    console.log('[Player] Language changed to:', language);
    // The global window.currentLanguage is already updated in main.js
    // We just need to re-render the visible slots with the new language.
    this.virtualize();
  }

  updateVideoSlotLanguage(item, video, language) {
    const titleElement = item.querySelector('.video-title');
    const descriptionElement = item.querySelector('.video-description');

    if (titleElement && video.title && typeof video.title === 'object') {
      titleElement.textContent = video.title[language] || video.title.en || 'No title';
    }

    if (descriptionElement && video.description && typeof video.description === 'object') {
      descriptionElement.textContent = video.description[language] || video.description.en || 'No description';
    }
  }

  destroy() {
    console.log('[Player] Destroying player instance.');

    // Clean up performance helper
    PerformanceHelper.cleanup();

    if (this.isFullscreen) {
      this.exitFullscreen();
    }

    this.saveCurrentProgress();

    // Remove event listeners
    if (this.container && this.boundHandleChangeVideo) {
      this.container.removeEventListener('changevideo', this.boundHandleChangeVideo);
    }
    if (this.wrapper && this.boundHandleClick) {
      this.wrapper.removeEventListener('click', this.boundHandleClick);
    }

    // Clean up video elements and progress bar listeners
    if (this.videoItems) {
      this.videoItems.forEach(item => {
        const video = item.querySelector('video');
        if (video) {
          this.removeProgressBarListeners(video);
          // Clean up video memory
          PerformanceHelper.cleanupVideo(video);
        }
      });
    }

    // Clean up components
    if (this.gestureManager && this.gestureManager.destroy) {
      this.gestureManager.destroy();
    }
    if (this.bottomMenu && this.bottomMenu.destroy) {
      this.bottomMenu.destroy();
    }
    if (this.houseDrawers) {
      this.houseDrawers.forEach(drawer => {
        if (drawer && drawer.destroy) {
          drawer.destroy();
        }
      });
    }
    if (this.fullscreenController && this.fullscreenController.destroy) {
      this.fullscreenController.destroy();
    }

    // Clear all references to prevent memory leaks
    this.videoItems = null;
    this.wrapper = null;
    this.container = null;
    this.dataManager = null;
    this.gestureManager = null;
    this.bottomMenu = null;
    this.houseDrawers = null;
    this.fullscreenController = null;
    this.progressMap = null;

    console.log('[Player] Player destroyed and memory cleaned up');
  }

  /**
   * Load user states for all videos
   */
  async loadUserStates() {
    try {
      console.log('[Player] Loading user states...');
      await this.dataManager.loadUserStates();

      // Update current video UI if states were loaded
      const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
      const currentSlot = this.videoItems[centerSlotIndex];
      const currentVideoData = this.dataManager.getVideo(this.currentIndex);

      if (currentVideoData && currentSlot) {
        updateVideoSlot(currentSlot, currentVideoData, window.currentLanguage || 'en');
      }

      console.log('[Player] User states loaded successfully');
    } catch (error) {
      console.error('[Player] Failed to load user states:', error);
    }
  }

  /**
   * Track video viewing progress
   */
  trackVideoProgress() {
    const centerSlotIndex = Math.floor(NUM_VIDEO_SLOTS / 2);
    const currentVideo = this.videoItems[centerSlotIndex]?.querySelector('video');
    const currentVideoData = this.dataManager.getVideo(this.currentIndex);

    if (!currentVideo || !currentVideoData) return;

    // Update progress every 5 seconds
    if (!this.progressTrackingInterval) {
      this.progressTrackingInterval = setInterval(() => {
        const currentVideo = this.videoItems[Math.floor(NUM_VIDEO_SLOTS / 2)]?.querySelector('video');
        const currentVideoData = this.dataManager.getVideo(this.currentIndex);

        if (currentVideo && currentVideoData && !currentVideo.paused) {
          const progressSeconds = Math.floor(currentVideo.currentTime);
          this.dataManager.updateVideoProgress(currentVideoData.id, progressSeconds);
        }
      }, 5000);
    }
  }

  /**
   * Stop tracking video progress
   */
  stopTrackingProgress() {
    if (this.progressTrackingInterval) {
      clearInterval(this.progressTrackingInterval);
      this.progressTrackingInterval = null;
    }
  }
}